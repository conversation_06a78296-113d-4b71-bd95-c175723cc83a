{"packages": [{"name": "czproject/git-php", "version": "v4.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/czproject/git-php.git", "reference": "3ea910e188849d5e239d65167010c05196310915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/czproject/git-php/zipball/3ea910e188849d5e239d65167010c05196310915", "reference": "3ea910e188849d5e239d65167010c05196310915", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "require-dev": {"nette/tester": "^2.4"}, "time": "2025-06-10T18:32:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "jan<PERSON><PERSON>@email.cz"}], "description": "Library for work with Git repository in PHP.", "keywords": ["git"], "support": {"issues": "https://github.com/czproject/git-php/issues", "source": "https://github.com/czproject/git-php/tree/v4.5.0"}, "funding": [{"url": "https://www.janpecha.cz/donate/git-php/", "type": "other"}, {"url": "https://donate.stripe.com/7sIcO2a9maTSg2A9AA", "type": "stripe"}], "install-path": "../czproject/git-php"}], "dev": false, "dev-package-names": []}