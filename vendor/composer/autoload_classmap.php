<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'CzProject\\GitPhp\\CommandProcessor' => $vendorDir . '/czproject/git-php/src/CommandProcessor.php',
    'CzProject\\GitPhp\\Commit' => $vendorDir . '/czproject/git-php/src/Commit.php',
    'CzProject\\GitPhp\\CommitId' => $vendorDir . '/czproject/git-php/src/CommitId.php',
    'CzProject\\GitPhp\\Exception' => $vendorDir . '/czproject/git-php/src/exceptions.php',
    'CzProject\\GitPhp\\Git' => $vendorDir . '/czproject/git-php/src/Git.php',
    'CzProject\\GitPhp\\GitException' => $vendorDir . '/czproject/git-php/src/exceptions.php',
    'CzProject\\GitPhp\\GitRepository' => $vendorDir . '/czproject/git-php/src/GitRepository.php',
    'CzProject\\GitPhp\\Helpers' => $vendorDir . '/czproject/git-php/src/Helpers.php',
    'CzProject\\GitPhp\\IRunner' => $vendorDir . '/czproject/git-php/src/IRunner.php',
    'CzProject\\GitPhp\\InvalidArgumentException' => $vendorDir . '/czproject/git-php/src/exceptions.php',
    'CzProject\\GitPhp\\InvalidStateException' => $vendorDir . '/czproject/git-php/src/exceptions.php',
    'CzProject\\GitPhp\\RunnerResult' => $vendorDir . '/czproject/git-php/src/RunnerResult.php',
    'CzProject\\GitPhp\\Runners\\CliRunner' => $vendorDir . '/czproject/git-php/src/Runners/CliRunner.php',
    'CzProject\\GitPhp\\Runners\\MemoryRunner' => $vendorDir . '/czproject/git-php/src/Runners/MemoryRunner.php',
    'CzProject\\GitPhp\\Runners\\OldGitRunner' => $vendorDir . '/czproject/git-php/src/Runners/OldGitRunner.php',
    'CzProject\\GitPhp\\StaticClassException' => $vendorDir . '/czproject/git-php/src/exceptions.php',
);
