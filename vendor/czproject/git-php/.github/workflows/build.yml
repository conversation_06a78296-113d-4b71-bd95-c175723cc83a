name: Build

on:
    push:
        branches:
            - master
        tags:
            - 'v*'

    pull_request:

jobs:
    tests:
        uses: janpecha/actions/.github/workflows/nette-tester-library.yml@master
        with:
            phpVersions: '["8.0", "8.1", "8.2", "8.3", "8.4"]'
            lowestDependencies: true

    coding-style:
        uses: janpecha/actions/.github/workflows/code-checker.yml@master

    static-analysis:
        uses: janpecha/actions/.github/workflows/phpstan.yml@master
        with:
            phpVersions: '["8.0", "8.1", "8.2", "8.3", "8.4"]'
