<?php

/**
 * Git operations class for WP Git Manager
 * Enhanced with czproject/git-php library
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

use Cz\Git\GitRepository;
use Cz\Git\GitException;

class WPGitManager_GitOperations {

    private $git_repo;
    private $repo_path;
    private $git_path;

    public function __construct()
    {
        $this->git_path = get_option('wpgm_git_path', '/usr/bin/git');
        $this->repo_path = get_option('wpgm_repo_path', ABSPATH);

        try {
            if (is_dir($this->repo_path . '/.git')) {
                $this->git_repo = new GitRepository($this->repo_path);
                if ($this->git_path !== '/usr/bin/git') {
                    $this->git_repo->setGitBinary($this->git_path);
                }
            }
        } catch (GitException $e) {
            error_log('WP Git Manager: Failed to initialize Git repository: ' . $e->getMessage());
        }
    }

    public function get_status()
    {
        if (!$this->is_git_available()) {
            return array('changes_count' => 0, 'status' => 'Git not available');
        }

        if (!$this->git_repo) {
            return array('changes_count' => 0, 'status' => 'Repository not initialized');
        }

        try {
            $status = $this->git_repo->getStatus();
            $changes = array_merge($status['added'], $status['modified'], $status['deleted'], $status['renamed']);

            return array(
                'changes_count' => count($changes),
                'status' => 'ok',
                'changes' => $changes,
                'detailed_status' => $status
            );
        } catch (GitException $e) {
            return array('changes_count' => 0, 'status' => 'Exception: ' . $e->getMessage());
        }
    }
    
    public function is_git_available() {
        $git_path = get_option('wpgm_git_path', '/usr/bin/git');
        return file_exists($git_path) && is_executable($git_path);
    }

    public function execute_command($command)
    {
        if (!$this->is_git_available()) {
            return array('success' => false, 'message' => 'Git not available');
        }

        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $parts = explode(' ', trim($command));
            $git_command = array_shift($parts);

            switch ($git_command) {
                case 'add':
                    if (in_array('-A', $parts) || in_array('--all', $parts)) {
                        $this->git_repo->addAllChanges();
                        return array('success' => true, 'message' => 'All changes added');
                    } else {
                        foreach ($parts as $file) {
                            $this->git_repo->addFile($file);
                        }
                        return array('success' => true, 'message' => 'Files added');
                    }

                case 'commit':
                    $message_index = array_search('-m', $parts);
                    if ($message_index !== false && isset($parts[$message_index + 1])) {
                        $message = $parts[$message_index + 1];
                        $this->git_repo->commit($message);
                        return array('success' => true, 'message' => 'Committed successfully');
                    }
                    return array('success' => false, 'message' => 'Commit message required');

                case 'push':
                    $remote = $parts[0] ?? 'origin';
                    $branch = $parts[1] ?? $this->git_repo->getCurrentBranchName();
                    $this->git_repo->push($remote, $branch);
                    return array('success' => true, 'message' => 'Pushed successfully');

                case 'pull':
                    $remote = $parts[0] ?? 'origin';
                    $branch = $parts[1] ?? $this->git_repo->getCurrentBranchName();
                    $this->git_repo->pull($remote, $branch);
                    return array('success' => true, 'message' => 'Pulled successfully');

                default:
                    $output = $this->git_repo->execute($command);
                    return array('success' => true, 'message' => implode("\n", $output));
            }
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Git error: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Exception: ' . $e->getMessage());
        }
    }

    public function check_setup_status()
    {
        $status = array(
            'is_complete' => false,
            'git_available' => false,
            'repo_exists' => false,
            'user_configured' => false,
            'has_remote' => false,
            'has_branch' => false
        );

        $status['git_available'] = $this->is_git_available();

        if (!$status['git_available']) {
            return $status;
        }

        if (is_dir($this->repo_path . '/.git')) {
            $status['repo_exists'] = true;

            try {
                if ($this->git_repo) {
                    // Check user configuration
                    $user_name = $this->git_repo->execute('config', 'user.name');
                    $user_email = $this->git_repo->execute('config', 'user.email');
                    if (!empty($user_name) && !empty($user_email)) {
                        $status['user_configured'] = true;
                    }

                    // Check for remotes
                    $remotes = $this->git_repo->execute('remote');
                    if (!empty($remotes)) {
                        $status['has_remote'] = true;
                    }

                    // Check for branches
                    $branches = $this->git_repo->getBranches();
                    if (!empty($branches)) {
                        $status['has_branch'] = true;
                    }
                }
            } catch (GitException $e) {
                error_log('WP Git Manager: Error checking setup status: ' . $e->getMessage());
            }
        }

        $status['is_complete'] = $status['git_available'] && $status['repo_exists'] &&
                                $status['user_configured'] && $status['has_branch'];

        return $status;
    }

    public function init_repository($git_path, $repo_path)
    {
        try {
            $repo = GitRepository::init($repo_path);
            if ($git_path !== '/usr/bin/git') {
                $repo->setGitBinary($git_path);
            }

            // Update instance variables
            $this->git_path = $git_path;
            $this->repo_path = $repo_path;
            $this->git_repo = $repo;

            return array('success' => true, 'message' => 'Repository initialized');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to initialize repository: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }

    public function set_user_config($git_path, $repo_path, $user_name, $user_email)
    {
        try {
            if (!$this->git_repo) {
                $this->git_repo = new GitRepository($repo_path);
                if ($git_path !== '/usr/bin/git') {
                    $this->git_repo->setGitBinary($git_path);
                }
            }

            $this->git_repo->execute('config', 'user.name', $user_name);
            $this->git_repo->execute('config', 'user.email', $user_email);

            return array('success' => true, 'message' => 'User configuration set');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to set user configuration: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }

    public function add_remote($git_path, $repo_path, $remote_name, $remote_url)
    {
        try {
            if (!$this->git_repo) {
                $this->git_repo = new GitRepository($repo_path);
                if ($git_path !== '/usr/bin/git') {
                    $this->git_repo->setGitBinary($git_path);
                }
            }

            $this->git_repo->addRemote($remote_name, $remote_url);

            return array('success' => true, 'message' => 'Remote added successfully');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to add remote: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }

    public function create_branch($git_path, $repo_path, $branch_name)
    {
        try {
            if (!$this->git_repo) {
                $this->git_repo = new GitRepository($repo_path);
                if ($git_path !== '/usr/bin/git') {
                    $this->git_repo->setGitBinary($git_path);
                }
            }

            // Create initial commit if needed
            $status = $this->git_repo->getStatus();
            $has_changes = !empty($status['added']) || !empty($status['modified']) || !empty($status['deleted']);

            if (!$has_changes) {
                $readme_file = $repo_path . '/README.md';
                if (!file_exists($readme_file)) {
                    file_put_contents($readme_file, "# WordPress Site\n\nThis repository contains a WordPress installation managed with WP Git Manager.\n");
                }
                $this->git_repo->addFile('README.md');
                $this->git_repo->commit('Initial commit');
            }

            // Create and switch to branch
            $this->git_repo->createBranch($branch_name, true);

            return array('success' => true, 'message' => 'Branch created and checked out');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to create branch: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }
    
    public function test_git_path($git_path) {
        if (file_exists($git_path) && is_executable($git_path)) {
            exec($git_path . ' --version 2>/dev/null', $output, $return);
            if ($return === 0) {
                return array('success' => true, 'version' => $output[0]);
            }
        }
        return array('success' => false, 'message' => 'Git not found or not executable');
    }

    // Enhanced methods using czproject/git-php

    public function get_branches()
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $branches = $this->git_repo->getBranches();
            $current_branch = $this->git_repo->getCurrentBranchName();

            return array(
                'success' => true,
                'branches' => $branches,
                'current_branch' => $current_branch
            );
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error getting branches: ' . $e->getMessage());
        }
    }

    public function get_commit_history($limit = 10)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $log = $this->git_repo->execute('log', '--oneline', '-' . $limit);
            return array('success' => true, 'commits' => $log);
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error getting commit history: ' . $e->getMessage());
        }
    }

    public function get_file_diff($file_path)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $diff = $this->git_repo->execute('diff', $file_path);
            return array('success' => true, 'diff' => implode("\n", $diff));
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error getting file diff: ' . $e->getMessage());
        }
    }

    public function stage_file($file_path)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $this->git_repo->addFile($file_path);
            return array('success' => true, 'message' => 'File staged successfully');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error staging file: ' . $e->getMessage());
        }
    }

    public function unstage_file($file_path)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $this->git_repo->execute('reset', 'HEAD', $file_path);
            return array('success' => true, 'message' => 'File unstaged successfully');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error unstaging file: ' . $e->getMessage());
        }
    }

    public function switch_branch($branch_name)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $this->git_repo->checkout($branch_name);
            return array('success' => true, 'message' => 'Switched to branch: ' . $branch_name);
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error switching branch: ' . $e->getMessage());
        }
    }
}