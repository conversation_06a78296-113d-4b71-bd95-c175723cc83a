/* WP Git Manager Styles */

/* Admin Bar Styles */
#wp-admin-bar-wp-git-manager .ab-item {
    color: #fff !important;
}

#wp-admin-bar-wp-git-manager .ab-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

#wp-admin-bar-wp-git-manager .ab-submenu {
    background-color: #23282d;
}

#wp-admin-bar-wp-git-manager .ab-submenu .ab-item {
    color: #eee;
    padding: 10px 15px;
}

#wp-admin-bar-wp-git-manager .ab-submenu .ab-item:hover {
    background-color: #0073aa;
    color: #fff;
}

/* Git Status Styles */
.git-status-info {
    background: #f1f1f1;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.git-file-list {
    margin: 20px 0;
}

.git-file-list h4 {
    margin: 15px 0 5px 0;
    color: #23282d;
}

.git-file-list ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.git-file-list li {
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.git-file-added {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.git-file-modified {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.git-file-deleted {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Button Styles */
.git-stage-file,
.git-unstage-file,
.git-view-diff,
.git-view-history,
.git-branch-switch {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 5px;
}

.git-stage-file:hover,
.git-unstage-file:hover,
.git-view-diff:hover,
.git-view-history:hover,
.git-branch-switch:hover {
    background: #005a87;
}

.git-unstage-file {
    background: #dc3545;
}

.git-unstage-file:hover {
    background: #c82333;
}

.git-view-diff {
    background: #6c757d;
}

.git-view-diff:hover {
    background: #5a6268;
}

/* Modal Styles */
.git-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.git-modal {
    background: #fff;
    border-radius: 4px;
    max-width: 80%;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.git-modal-header {
    background: #f1f1f1;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.git-modal-header h3 {
    margin: 0;
    color: #23282d;
}

.git-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.git-modal-close:hover {
    color: #000;
}

.git-modal-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.git-modal-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
}

/* Setup Page Styles */
.git-setup-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin: 20px 0;
    padding: 20px;
}

.git-setup-section h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.git-setup-form {
    display: grid;
    gap: 15px;
}

.git-setup-field {
    display: flex;
    flex-direction: column;
}

.git-setup-field label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #23282d;
}

.git-setup-field input[type="text"],
.git-setup-field input[type="email"],
.git-setup-field input[type="url"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.git-setup-field input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.git-setup-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.git-setup-btn {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.git-setup-btn:hover {
    background: #005a87;
}

.git-setup-btn.secondary {
    background: #6c757d;
}

.git-setup-btn.secondary:hover {
    background: #5a6268;
}

/* Branch Management */
.git-branches {
    margin: 20px 0;
}

.git-branches h4 {
    margin-bottom: 10px;
    color: #23282d;
}

.git-branch-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.git-branch-item {
    padding: 8px 12px;
    margin: 2px 0;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.git-branch-item.current {
    background: #d1ecf1;
    border-color: #bee5eb;
    font-weight: 600;
}

.git-branch-name {
    flex-grow: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .git-modal {
        max-width: 95%;
        max-height: 90%;
    }
    
    .git-modal-content {
        padding: 15px;
    }
    
    .git-file-list li {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .git-setup-actions {
        flex-direction: column;
    }
}

/* Notices */
.notice.git-notice {
    margin: 5px 0;
}

.notice.git-notice p {
    margin: 0.5em 0;
}

/* Loading States */
.git-loading {
    opacity: 0.6;
    pointer-events: none;
}

.git-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: git-spin 1s linear infinite;
}

@keyframes git-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
