jQuery(document).ready(function($) {
    'use strict';
    
    // Git Manager functionality
    const GitManager = {
        
        init: function() {
            this.bindEvents();
            this.updateStatus();
        },
        
        bindEvents: function() {
            // Admin bar menu items
            $('.git-commit-btn').on('click', this.handleCommit);
            $('.git-push-btn').on('click', this.handlePush);
            $('.git-pull-btn').on('click', this.handlePull);
            
            // Enhanced features
            $(document).on('click', '.git-branch-switch', this.handleBranchSwitch);
            $(document).on('click', '.git-stage-file', this.handleStageFile);
            $(document).on('click', '.git-unstage-file', this.handleUnstageFile);
            $(document).on('click', '.git-view-diff', this.handleViewDiff);
            $(document).on('click', '.git-view-history', this.handleViewHistory);
            
            // Setup page events
            $(document).on('click', '#test-git-path', this.testGitPath);
            $(document).on('click', '#init-repo', this.initRepo);
            $(document).on('click', '#set-user', this.setUser);
            $(document).on('click', '#add-remote', this.addRemote);
            $(document).on('click', '#create-branch', this.createBranch);
            $(document).on('click', '#save-settings', this.saveSettings);
        },
        
        updateStatus: function() {
            this.ajaxRequest('git_status', {}, function(response) {
                if (response.success) {
                    const status = response.data;
                    $('.git-status-info').html(
                        '<strong>Changes:</strong> ' + status.changes_count + 
                        ' | <strong>Status:</strong> ' + status.status
                    );
                    
                    if (status.detailed_status) {
                        GitManager.updateFileList(status.detailed_status);
                    }
                }
            });
        },
        
        updateFileList: function(status) {
            let html = '';
            
            if (status.added && status.added.length > 0) {
                html += '<h4>Added Files:</h4><ul>';
                status.added.forEach(function(file) {
                    html += '<li class="git-file-added">' + file + 
                           ' <button class="git-unstage-file" data-file="' + file + '">Unstage</button></li>';
                });
                html += '</ul>';
            }
            
            if (status.modified && status.modified.length > 0) {
                html += '<h4>Modified Files:</h4><ul>';
                status.modified.forEach(function(file) {
                    html += '<li class="git-file-modified">' + file + 
                           ' <button class="git-stage-file" data-file="' + file + '">Stage</button>' +
                           ' <button class="git-view-diff" data-file="' + file + '">View Diff</button></li>';
                });
                html += '</ul>';
            }
            
            if (status.deleted && status.deleted.length > 0) {
                html += '<h4>Deleted Files:</h4><ul>';
                status.deleted.forEach(function(file) {
                    html += '<li class="git-file-deleted">' + file + 
                           ' <button class="git-stage-file" data-file="' + file + '">Stage</button></li>';
                });
                html += '</ul>';
            }
            
            $('.git-file-list').html(html);
        },
        
        handleCommit: function(e) {
            e.preventDefault();
            
            const message = prompt(wpGitManager.strings.commit_message);
            if (!message) return;
            
            GitManager.ajaxRequest('git_commit', { message: message }, function(response) {
                if (response.success) {
                    GitManager.showNotice(wpGitManager.strings.commit_success, 'success');
                    GitManager.updateStatus();
                } else {
                    GitManager.showNotice(wpGitManager.strings.commit_error + ': ' + response.data, 'error');
                }
            });
        },
        
        handlePush: function(e) {
            e.preventDefault();
            
            if (!confirm(wpGitManager.strings.confirm_push)) return;
            
            GitManager.ajaxRequest('git_push', {}, function(response) {
                if (response.success) {
                    GitManager.showNotice(wpGitManager.strings.push_success, 'success');
                } else {
                    GitManager.showNotice(wpGitManager.strings.push_error + ': ' + response.data, 'error');
                }
            });
        },
        
        handlePull: function(e) {
            e.preventDefault();
            
            if (!confirm(wpGitManager.strings.confirm_pull)) return;
            
            GitManager.ajaxRequest('git_pull', {}, function(response) {
                if (response.success) {
                    GitManager.showNotice(wpGitManager.strings.pull_success, 'success');
                    GitManager.updateStatus();
                } else {
                    GitManager.showNotice(wpGitManager.strings.pull_error + ': ' + response.data, 'error');
                }
            });
        },
        
        handleStageFile: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            
            GitManager.ajaxRequest('git_stage_file', { file: file }, function(response) {
                if (response.success) {
                    GitManager.showNotice('File staged: ' + file, 'success');
                    GitManager.updateStatus();
                } else {
                    GitManager.showNotice('Error staging file: ' + response.data, 'error');
                }
            });
        },
        
        handleUnstageFile: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            
            GitManager.ajaxRequest('git_unstage_file', { file: file }, function(response) {
                if (response.success) {
                    GitManager.showNotice('File unstaged: ' + file, 'success');
                    GitManager.updateStatus();
                } else {
                    GitManager.showNotice('Error unstaging file: ' + response.data, 'error');
                }
            });
        },
        
        handleViewDiff: function(e) {
            e.preventDefault();
            const file = $(this).data('file');
            
            GitManager.ajaxRequest('git_file_diff', { file: file }, function(response) {
                if (response.success) {
                    GitManager.showDiffModal(file, response.data.diff);
                } else {
                    GitManager.showNotice('Error getting diff: ' + response.data, 'error');
                }
            });
        },
        
        handleViewHistory: function(e) {
            e.preventDefault();
            
            GitManager.ajaxRequest('git_commit_history', {}, function(response) {
                if (response.success) {
                    GitManager.showHistoryModal(response.data.commits);
                } else {
                    GitManager.showNotice('Error getting history: ' + response.data, 'error');
                }
            });
        },
        
        handleBranchSwitch: function(e) {
            e.preventDefault();
            const branch = $(this).data('branch');
            
            GitManager.ajaxRequest('git_switch_branch', { branch: branch }, function(response) {
                if (response.success) {
                    GitManager.showNotice('Switched to branch: ' + branch, 'success');
                    location.reload();
                } else {
                    GitManager.showNotice('Error switching branch: ' + response.data, 'error');
                }
            });
        },
        
        showDiffModal: function(file, diff) {
            const modal = $('<div class="git-modal-overlay"><div class="git-modal">' +
                          '<div class="git-modal-header"><h3>Diff: ' + file + '</h3>' +
                          '<button class="git-modal-close">&times;</button></div>' +
                          '<div class="git-modal-content"><pre>' + diff + '</pre></div>' +
                          '</div></div>');
            
            $('body').append(modal);
            modal.find('.git-modal-close').on('click', function() {
                modal.remove();
            });
        },
        
        showHistoryModal: function(commits) {
            let html = '<ul>';
            commits.forEach(function(commit) {
                html += '<li>' + commit + '</li>';
            });
            html += '</ul>';
            
            const modal = $('<div class="git-modal-overlay"><div class="git-modal">' +
                          '<div class="git-modal-header"><h3>Commit History</h3>' +
                          '<button class="git-modal-close">&times;</button></div>' +
                          '<div class="git-modal-content">' + html + '</div>' +
                          '</div></div>');
            
            $('body').append(modal);
            modal.find('.git-modal-close').on('click', function() {
                modal.remove();
            });
        },
        
        showNotice: function(message, type) {
            const notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap').prepend(notice);
            
            setTimeout(function() {
                notice.fadeOut();
            }, 5000);
        },
        
        ajaxRequest: function(action, data, callback) {
            const requestData = {
                action: action,
                nonce: wpGitManager.nonce,
                ...data
            };
            
            $.post(wpGitManager.ajax_url, requestData, callback);
        },
        
        // Setup page methods
        testGitPath: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            
            GitManager.ajaxRequest('git_setup_check', {
                check_type: 'git_path',
                git_path: gitPath
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice('Git found: ' + response.data.version, 'success');
                } else {
                    GitManager.showNotice('Git test failed: ' + response.data, 'error');
                }
            });
        },
        
        initRepo: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            const repoPath = $('#repo-path').val();
            
            GitManager.ajaxRequest('git_init_repo', {
                git_path: gitPath,
                repo_path: repoPath
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice(response.data, 'success');
                } else {
                    GitManager.showNotice('Init failed: ' + response.data, 'error');
                }
            });
        },
        
        setUser: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            const repoPath = $('#repo-path').val();
            const userName = $('#user-name').val();
            const userEmail = $('#user-email').val();
            
            GitManager.ajaxRequest('git_set_user', {
                git_path: gitPath,
                repo_path: repoPath,
                user_name: userName,
                user_email: userEmail
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice(response.data, 'success');
                } else {
                    GitManager.showNotice('User config failed: ' + response.data, 'error');
                }
            });
        },
        
        addRemote: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            const repoPath = $('#repo-path').val();
            const remoteName = $('#remote-name').val();
            const remoteUrl = $('#remote-url').val();
            
            GitManager.ajaxRequest('git_add_remote', {
                git_path: gitPath,
                repo_path: repoPath,
                remote_name: remoteName,
                remote_url: remoteUrl
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice(response.data, 'success');
                } else {
                    GitManager.showNotice('Add remote failed: ' + response.data, 'error');
                }
            });
        },
        
        createBranch: function(e) {
            e.preventDefault();
            const gitPath = $('#git-path').val();
            const repoPath = $('#repo-path').val();
            const branchName = $('#branch-name').val();
            
            GitManager.ajaxRequest('git_create_branch', {
                git_path: gitPath,
                repo_path: repoPath,
                branch_name: branchName
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice(response.data, 'success');
                } else {
                    GitManager.showNotice('Create branch failed: ' + response.data, 'error');
                }
            });
        },
        
        saveSettings: function(e) {
            e.preventDefault();
            
            const settings = {
                git_path: $('#git-path').val(),
                repo_path: $('#repo-path').val(),
                remote_name: $('#remote-name').val(),
                branch_name: $('#branch-name').val()
            };
            
            GitManager.ajaxRequest('git_setup_check', {
                check_type: 'save_settings',
                settings: settings
            }, function(response) {
                if (response.success) {
                    GitManager.showNotice('Settings saved successfully!', 'success');
                } else {
                    GitManager.showNotice('Save failed: ' + response.data, 'error');
                }
            });
        }
    };
    
    // Initialize Git Manager
    GitManager.init();
});
