# WP Git Manager 2.0

A powerful WordPress plugin that integrates Git functionality directly into your WordPress admin interface, enhanced with the robust czproject/git-php library for improved reliability and performance.

## Features

### Core Git Operations
- **Quick Commit**: Commit changes directly from the WordPress admin bar
- **Push/Pull**: Synchronize with remote repositories with one click
- **Real-time Status**: View repository status and file changes in real-time
- **Branch Management**: Create, switch, and manage Git branches
- **File Staging**: Stage and unstage individual files
- **Diff Viewing**: View file differences in a modal interface
- **Commit History**: Browse recent commits with detailed information

### Enhanced Features (New in 2.0)
- **czproject/git-php Integration**: Replaced shell commands with a robust PHP Git library
- **Improved Error Handling**: Better error messages and exception handling
- **Enhanced UI**: Modern, responsive interface with improved user experience
- **Branch Switching**: Switch between branches directly from the admin interface
- **File-level Operations**: Stage/unstage individual files with granular control
- **Visual Diff Viewer**: View file changes in a clean, formatted interface
- **Commit History Browser**: Browse and view commit history with pagination

### Setup & Configuration
- **Guided Setup**: Step-by-step setup wizard for first-time configuration
- **Git Path Detection**: Automatic detection and validation of Git installation
- **Repository Initialization**: Initialize Git repositories directly from WordPress
- **Remote Configuration**: Easy setup of remote repositories (GitHub, GitLab, etc.)
- **User Configuration**: Set Git user name and email for commits
- **Gitignore Management**: Built-in .gitignore editor with WordPress-specific templates

## Installation

1. **Upload the Plugin**
   ```bash
   # Via WordPress admin
   Upload the plugin zip file through Plugins > Add New > Upload Plugin
   
   # Via FTP
   Extract and upload the wp-git-manager folder to /wp-content/plugins/
   ```

2. **Install Dependencies**
   ```bash
   cd /path/to/wp-content/plugins/wp-git-manager
   composer install
   ```

3. **Activate the Plugin**
   - Go to Plugins in your WordPress admin
   - Find "WP Git Manager 2.0" and click Activate

4. **Complete Setup**
   - Navigate to Tools > Git Setup
   - Follow the guided setup wizard
   - Configure Git path, repository settings, and user information

## Requirements

- **PHP**: 7.4 or higher
- **WordPress**: 5.0 or higher
- **Git**: Installed on the server (typically `/usr/bin/git`)
- **Composer**: For dependency management
- **File Permissions**: Write access to the WordPress directory

## Configuration

### Basic Settings
Access settings via Settings > Git Manager:

- **Git Path**: Path to Git executable (usually `/usr/bin/git`)
- **Repository Path**: WordPress installation directory
- **Remote Name**: Default remote name (usually `origin`)
- **Branch Name**: Default branch name (`main`, `master`, etc.)
- **Auto Add**: Automatically stage all files before committing

### Advanced Settings
- **Commit Author**: Override Git user configuration for commits
- **Default Commit Message**: Pre-fill commit message dialogs
- **Gitignore Configuration**: Manage excluded files and patterns

## Usage

### Admin Bar Integration
The plugin adds a "Git" menu to the WordPress admin bar with:
- **Commit Changes**: Quick commit with custom message
- **Push**: Push changes to remote repository
- **Pull**: Pull changes from remote repository
- **Settings**: Access plugin configuration

### File Management
From the Settings page, you can:
- View all changed files with their status
- Stage/unstage individual files
- View diffs for modified files
- Browse commit history

### Branch Management
- View all available branches
- Switch between branches
- Create new branches
- See current branch status

## Technical Improvements

### czproject/git-php Integration
The plugin now uses the czproject/git-php library instead of shell commands:

```php
// Old approach (shell commands)
exec('git status --porcelain', $output);

// New approach (czproject/git-php)
$status = $this->git_repo->getStatus();
```

### Benefits:
- **Better Error Handling**: Proper exception handling with detailed error messages
- **Type Safety**: Structured data instead of parsing command output
- **Performance**: Reduced overhead from shell command execution
- **Security**: No shell injection vulnerabilities
- **Reliability**: Consistent behavior across different server environments

### Enhanced Architecture
- **Modular Design**: Separated concerns with dedicated classes
- **AJAX Endpoints**: RESTful AJAX handlers for all Git operations
- **Event-Driven UI**: Modern JavaScript with proper event handling
- **Responsive Design**: Mobile-friendly interface with CSS Grid/Flexbox

## File Structure

```
wp-git-manager/
├── wp-git-manager.php          # Main plugin file
├── composer.json               # Dependency management
├── includes/
│   ├── class-git-operations.php    # Enhanced Git operations
│   └── class-ajax-handlers.php     # AJAX request handlers
├── admin/
│   ├── setup-page.php          # Setup wizard
│   └── settings-page.php       # Settings interface
├── assets/
│   ├── wp-git-manager.js       # Frontend JavaScript
│   └── wp-git-manager.css      # Styling
└── vendor/                     # Composer dependencies
    └── czproject/git-php/      # Git library
```

## Security Considerations

- **Capability Checks**: All operations require `manage_options` capability
- **Nonce Verification**: CSRF protection on all AJAX requests
- **Input Sanitization**: All user inputs are properly sanitized
- **File Path Validation**: Repository paths are validated and restricted
- **No Shell Injection**: czproject/git-php eliminates shell command risks

## Troubleshooting

### Common Issues

1. **Git Not Found**
   - Verify Git is installed: `which git`
   - Update Git path in settings
   - Check file permissions

2. **Permission Denied**
   - Ensure WordPress has write access to the directory
   - Check Git repository permissions
   - Verify user ownership

3. **Composer Dependencies**
   - Run `composer install` in plugin directory
   - Ensure PHP has required extensions
   - Check for conflicting plugins

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

GPL v2 or later - same as WordPress

## Changelog

### Version 2.0.0
- **NEW**: czproject/git-php integration
- **NEW**: Enhanced file staging/unstaging
- **NEW**: Visual diff viewer
- **NEW**: Branch management interface
- **NEW**: Commit history browser
- **IMPROVED**: Error handling and user feedback
- **IMPROVED**: Modern, responsive UI
- **IMPROVED**: Security and performance
- **FIXED**: Various stability issues

### Version 1.0.0
- Initial release with basic Git functionality

## Support

For support, please:
1. Check the troubleshooting section
2. Review WordPress and PHP error logs
3. Create an issue on the project repository
4. Include relevant error messages and system information
