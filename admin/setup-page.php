<?php
/**
 * Setup page for WP Git Manager
 */

if (!current_user_can('manage_options')) {
    wp_die('Unauthorized');
}
?>

<div class="wrap">
    <h1>Git Manager Setup</h1>
    
    <div id="setup-container">
        <div class="setup-step" id="step-1">
            <h2>Step 1: Git Configuration</h2>
            <p>First, let's verify that Git is available on your server.</p>
            
            <table class="form-table">
                <tr>
                    <th scope="row">Git Path</th>
                    <td>
                        <input type="text" id="git-path" class="regular-text" value="<?php echo esc_attr(get_option('wpgm_git_path', '/usr/bin/git')); ?>" />
                        <p class="description">Path to the Git executable on your server.</p>
                        <div id="git-path-status"></div>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Repository Path</th>
                    <td>
                        <input type="text" id="repo-path" class="regular-text" value="<?php echo esc_attr(get_option('wpgm_repo_path', ABSPATH)); ?>" />
                        <p class="description">Path to your WordPress installation directory.</p>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <button type="button" class="button button-primary" id="test-git">Test Git Path</button>
                <button type="button" class="button" id="check-setup" style="display:none;">Check Setup Status</button>
            </p>
        </div>
        
        <div class="setup-step" id="step-2" style="display:none;">
            <h2>Step 2: Repository Setup</h2>
            <div id="setup-status"></div>
            
            <div id="repo-actions" style="display:none;">
                <h3>Initialize Repository</h3>
                <p>Your directory is not a Git repository. Click below to initialize it.</p>
                <button type="button" class="button button-primary" id="init-repo">Initialize Repository</button>
            </div>
            
            <div id="user-config" style="display:none;">
                <h3>Git User Configuration</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">User Name</th>
                        <td>
                            <input type="text" id="git-user-name" class="regular-text" placeholder="Your Name" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">User Email</th>
                        <td>
                            <input type="email" id="git-user-email" class="regular-text" placeholder="<EMAIL>" />
                        </td>
                    </tr>
                </table>
                <button type="button" class="button button-primary" id="set-user">Set User Configuration</button>
            </div>
            
            <div id="remote-config" style="display:none;">
                <h3>Remote Repository (Optional)</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Remote Name</th>
                        <td>
                            <input type="text" id="remote-name" class="regular-text" value="origin" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Remote URL</th>
                        <td>
                            <input type="url" id="remote-url" class="regular-text" placeholder="https://github.com/username/repo.git" />
                            <p class="description">Optional: Add a remote repository URL (GitHub, GitLab, etc.)</p>
                        </td>
                    </tr>
                </table>
                <button type="button" class="button" id="add-remote">Add Remote</button>
                <button type="button" class="button" id="skip-remote">Skip Remote Setup</button>
            </div>
            
            <div id="branch-config" style="display:none;">
                <h3>Initial Branch</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Branch Name</th>
                        <td>
                            <input type="text" id="branch-name" class="regular-text" value="main" />
                        </td>
                    </tr>
                </table>
                <button type="button" class="button button-primary" id="create-branch">Create Branch</button>
            </div>
        </div>
        
        <div class="setup-step" id="step-3" style="display:none;">
            <h2>Step 3: Final Configuration</h2>
            <p>Save your settings and complete the setup.</p>
            
            <div id="final-settings">
                <table class="form-table">
                    <tr>
                        <th scope="row">Auto-add Files</th>
                        <td>
                            <label>
                                <input type="checkbox" id="auto-add" checked />
                                Automatically add all files when committing
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Exclude Files</th>
                        <td>
                            <textarea id="gitignore-content" rows="10" cols="50" placeholder="wp-config.php&#10;*.log&#10;node_modules/&#10;.DS_Store"></textarea>
                            <p class="description">Files and patterns to exclude from Git (will create/update .gitignore)</p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <p class="submit">
                <button type="button" class="button button-primary" id="complete-setup">Complete Setup</button>
            </p>
        </div>
        
        <div id="setup-complete" style="display:none;">
            <div class="notice notice-success">
                <p><strong>Setup Complete!</strong> Your Git repository is now ready to use.</p>
                <p><a href="<?php echo admin_url('options-general.php?page=wp-git-manager-settings'); ?>" class="button">Go to Settings</a></p>
            </div>
        </div>
    </div>
</div>

<style>
.setup-step {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.setup-step h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#setup-status {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.status-item {
    margin-bottom: 10px;
    padding: 5px 10px;
    border-radius: 3px;
}

.status-item.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-item.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-item.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

#git-path-status {
    margin-top: 10px;
}
</style>

<script type="text/javascript">
jQuery(document).ready(function($) {
    
    $('#test-git').on('click', function() {
        var gitPath = $('#git-path').val();
        var button = $(this);
        
        button.prop('disabled', true).text('Testing...');
        
        $.post(ajaxurl, {
            action: 'git_setup_check',
            nonce: wpGitManager.nonce,
            check_type: 'git_path',
            git_path: gitPath
        })
        .done(function(response) {
            if (response.success) {
                $('#git-path-status').html('<div class="notice notice-success inline"><p>✓ Git found: ' + response.data.version + '</p></div>');
                $('#check-setup').show();
            } else {
                $('#git-path-status').html('<div class="notice notice-error inline"><p>✗ ' + response.data + '</p></div>');
            }
        })
        .fail(function() {
            $('#git-path-status').html('<div class="notice notice-error inline"><p>✗ Failed to test Git path</p></div>');
        })
        .always(function() {
            button.prop('disabled', false).text('Test Git Path');
        });
    });
    
    $('#check-setup').on('click', function() {
        var gitPath = $('#git-path').val();
        var repoPath = $('#repo-path').val();
        var button = $(this);
        
        button.prop('disabled', true).text('Checking...');
        
        $.post(ajaxurl, {
            action: 'git_setup_check',
            nonce: wpGitManager.nonce,
            check_type: 'full_status',
            git_path: gitPath,
            repo_path: repoPath
        })
        .done(function(response) {
            if (response.success) {
                displaySetupStatus(response.data);
                $('#step-2').show();
            }
        })
        .always(function() {
            button.prop('disabled', false).text('Check Setup Status');
        });
    });
    
    function displaySetupStatus(status) {
        var html = '<h3>Setup Status</h3>';
        
        html += '<div class="status-item ' + (status.git_available ? 'success' : 'error') + '">';
        html += (status.git_available ? '✓' : '✗') + ' Git Available</div>';
        
        html += '<div class="status-item ' + (status.repo_exists ? 'success' : 'warning') + '">';
        html += (status.repo_exists ? '✓' : '○') + ' Git Repository</div>';
        
        html += '<div class="status-item ' + (status.user_configured ? 'success' : 'warning') + '">';
        html += (status.user_configured ? '✓' : '○') + ' User Configuration</div>';
        
        html += '<div class="status-item ' + (status.has_remote ? 'success' : 'warning') + '">';
        html += (status.has_remote ? '✓' : '○') + ' Remote Repository</div>';
        
        html += '<div class="status-item ' + (status.has_branch ? 'success' : 'warning') + '">';
        html += (status.has_branch ? '✓' : '○') + ' Initial Branch</div>';
        
        $('#setup-status').html(html);
        
        // Show appropriate action sections
        if (!status.repo_exists) {
            $('#repo-actions').show();
        } else if (!status.user_configured) {
            $('#user-config').show();
        } else if (!status.has_remote) {
            $('#remote-config').show();
        } else if (!status.has_branch) {
            $('#branch-config').show();
        } else {
            $('#step-3').show();
        }
    }
    
    // Handle repository initialization
    $('#init-repo').on('click', function() {
        var button = $(this);
        button.prop('disabled', true).text('Initializing...');
        
        $.post(ajaxurl, {
            action: 'git_init_repo',
            nonce: wpGitManager.nonce,
            git_path: $('#git-path').val(),
            repo_path: $('#repo-path').val()
        })
        .done(function(response) {
            if (response.success) {
                alert('Repository initialized successfully!');
                $('#repo-actions').hide();
                $('#user-config').show();
            } else {
                alert('Error: ' + response.data);
            }
        })
        .always(function() {
            button.prop('disabled', false).text('Initialize Repository');
        });
    });
    
    // Handle user configuration
    $('#set-user').on('click', function() {
        var userName = $('#git-user-name').val();
        var userEmail = $('#git-user-email').val();
        
        if (!userName || !userEmail) {
            alert('Please enter both name and email');
            return;
        }
        
        var button = $(this);
        button.prop('disabled', true).text('Setting...');
        
        $.post(ajaxurl, {
            action: 'git_set_user',
            nonce: wpGitManager.nonce,
            git_path: $('#git-path').val(),
            repo_path: $('#repo-path').val(),
            user_name: userName,
            user_email: userEmail
        })
        .done(function(response) {
            if (response.success) {
                alert('User configuration set successfully!');
                $('#user-config').hide();
                $('#remote-config').show();
            } else {
                alert('Error: ' + response.data);
            }
        })
        .always(function() {
            button.prop('disabled', false).text('Set User Configuration');
        });
    });
    
    // Handle remote addition
    $('#add-remote').on('click', function() {
        var remoteName = $('#remote-name').val();
        var remoteUrl = $('#remote-url').val();
        
        if (!remoteName || !remoteUrl) {
            alert('Please enter both remote name and URL');
            return;
        }
        
        var button = $(this);
        button.prop('disabled', true).text('Adding...');
        
        $.post(ajaxurl, {
            action: 'git_add_remote',
            nonce: wpGitManager.nonce,
            git_path: $('#git-path').val(),
            repo_path: $('#repo-path').val(),
            remote_name: remoteName,
            remote_url: remoteUrl
        })
        .done(function(response) {
            if (response.success) {
                alert('Remote added successfully!');
                $('#remote-config').hide();
                $('#branch-config').show();
            } else {
                alert('Error: ' + response.data);
            }
        })
        .always(function() {
            button.prop('disabled', false).text('Add Remote');
        });
    });
    
    $('#skip-remote').on('click', function() {
        $('#remote-config').hide();
        $('#branch-config').show();
    });
    
    // Handle branch creation
    $('#create-branch').on('click', function() {
        var branchName = $('#branch-name').val();
        
        if (!branchName) {
            alert('Please enter a branch name');
            return;
        }
        
        var button = $(this);
        button.prop('disabled', true).text('Creating...');
        
        $.post(ajaxurl, {
            action: 'git_create_branch',
            nonce: wpGitManager.nonce,
            git_path: $('#git-path').val(),
            repo_path: $('#repo-path').val(),
            branch_name: branchName
        })
        .done(function(response) {
            if (response.success) {
                alert('Branch created successfully!');
                $('#branch-config').hide();
                $('#step-3').show();
            } else {
                alert('Error: ' + response.data);
            }
        })
        .always(function() {
            button.prop('disabled', false).text('Create Branch');
        });
    });
    
    // Handle setup completion
    $('#complete-setup').on('click', function() {
        var settings = {
            git_path: $('#git-path').val(),
            repo_path: $('#repo-path').val(),
            remote_name: $('#remote-name').val() || 'origin',
            branch_name: $('#branch-name').val() || 'main',
            auto_add: $('#auto-add').is(':checked') ? '1' : '0'
        };
        
        // Create .gitignore if content provided
        var gitignoreContent = $('#gitignore-content').val();
        if (gitignoreContent) {
            settings.gitignore_content = gitignoreContent;
        }
        
        var button = $(this);
        button.prop('disabled', true).text('Completing...');
        
        $.post(ajaxurl, {
            action: 'git_setup_check',
            nonce: wpGitManager.nonce,
            check_type: 'save_settings',
            settings: settings
        })
        .done(function(response) {
            if (response.success) {
                $('#step-3').hide();
                $('#setup-complete').show();
            } else {
                alert('Error saving settings: ' + response.data);
            }
        })
        .always(function() {
            button.prop('disabled', false).text('Complete Setup');
        });
    });
});
</script>